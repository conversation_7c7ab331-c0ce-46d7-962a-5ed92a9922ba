// 富有支付订单号生成函数 - 替换 paymentController.js 中第1123行附近的代码

// 原代码：
// const finalOrderId = orderId || `FY${Date.now()}${Math.random().toString(36).substr(2, 9)}`;

// 新代码：
// 生成符合富有支付规则的订单号
const generateFuiouOrderId = () => {
  const prefix = process.env.FUIOU_ORDER_PREFIX || '18927'; // 5位商户号编码
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // yyyyMMdd
  const randomNum = Math.random().toString().slice(2, 10); // 8位随机数
  return `${prefix}${date}${randomNum}`;
};

const finalOrderId = orderId || generateFuiouOrderId();
