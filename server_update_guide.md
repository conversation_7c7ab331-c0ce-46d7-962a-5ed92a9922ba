# 服务器文件修改指南

## 操作步骤

### 1. 连接服务器并进入项目目录
```bash
ssh root@************
cd /www/wwwroot/writerpro.cn
```

### 2. 备份原文件
```bash
cp backend/src/controllers/paymentController.js backend/src/controllers/paymentController.js.backup
cp frontend/script.js frontend/script.js.backup
cp backend/src/config/fuiouConfig.js backend/src/config/fuiouConfig.js.backup
```

### 3. 修改后端支付控制器 (paymentController.js)
```bash
nano backend/src/controllers/paymentController.js
```
找到第1123行附近：
```javascript
const finalOrderId = orderId || `FY${Date.now()}${Math.random().toString(36).substr(2, 9)}`;
```
替换为：
```javascript
// 生成符合富有支付规则的订单号
const generateFuiouOrderId = () => {
  const prefix = process.env.FUIOU_ORDER_PREFIX || '18927'; // 5位商户号编码
  const date = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // yyyyMMdd
  const randomNum = Math.random().toString().slice(2, 10); // 8位随机数
  return `${prefix}${date}${randomNum}`;
};

const finalOrderId = orderId || generateFuiouOrderId();
```

### 4. 修改前端脚本 (script.js)
```bash
nano frontend/script.js
```
找到第2104行附近：
```javascript
function generateOrderId() {
    return 'FY' + Date.now() + Math.random().toString(36).substr(2, 9);
}
```
替换为：
```javascript
// 生成订单ID - 符合富有支付规则
function generateOrderId() {
    const prefix = '18927'; // 5位商户号编码
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // yyyyMMdd
    const randomNum = Math.random().toString().slice(2, 10); // 8位随机数
    return `${prefix}${date}${randomNum}`;
}
```

### 5. 修改富有支付配置 (fuiouConfig.js)
```bash
nano backend/src/config/fuiouConfig.js
```
找到第82-94行的 generateMerchantOrderNo 函数，替换为：
```javascript
// 生成商户订单号 - 符合富有支付规则
generateMerchantOrderNo() {
    const prefix = this.orderPrefix || '18927'; // 5位商户号编码
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // yyyyMMdd
    const randomNum = Math.random().toString().slice(2, 10); // 8位随机数
    
    // 流水号规则：商户号编码（5位）+日期（8位）+随机数（8位）
    return `${prefix}${date}${randomNum}`;
}
```

### 6. 确认环境变量
```bash
nano backend/.env
```
确保包含：
```
FUIOU_ORDER_PREFIX=18927
```

### 7. 重启服务
```bash
pm2 restart writerpro-backend
pm2 status
```

## 新订单号格式
- 商户号编码（5位）：18927
- 日期（8位）：yyyyMMdd
- 随机数（8位）：随机数字
- 示例：1892720250731123456789
