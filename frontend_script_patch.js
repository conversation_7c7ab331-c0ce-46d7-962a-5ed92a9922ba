// 前端订单号生成函数 - 替换 frontend/script.js 中第2104行附近的代码

// 原代码：
// function generateOrderId() {
//     return 'FY' + Date.now() + Math.random().toString(36).substr(2, 9);
// }

// 新代码：
// 生成订单ID - 符合富有支付规则
function generateOrderId() {
    const prefix = '18927'; // 5位商户号编码
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // yyyyMMdd
    const randomNum = Math.random().toString().slice(2, 10); // 8位随机数
    return `${prefix}${date}${randomNum}`;
}
