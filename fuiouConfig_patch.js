// 富有支付配置文件修改 - 替换 backend/src/config/fuiouConfig.js 中的 generateMerchantOrderNo 函数

// 原代码：
// generateMerchantOrderNo() {
//     const date = new Date();
//     const dateStr = date.getFullYear().toString() +
//                    (date.getMonth() + 1).toString().padStart(2, '0') +
//                    date.getDate().toString().padStart(2, '0');
//     const timeStr = date.getHours().toString().padStart(2, '0') +
//                    date.getMinutes().toString().padStart(2, '0') +
//                    date.getSeconds().toString().padStart(2, '0');
//     const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
//
//     // 流水号规则：订单前缀（5位）+日期（8位）+其他随机数（8-17位）
//     return `${this.orderPrefix}${dateStr}${timeStr}${randomStr}`;
// }

// 新代码：
// 生成商户订单号 - 符合富有支付规则
generateMerchantOrderNo() {
    const prefix = this.orderPrefix || '18927'; // 5位商户号编码
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, ''); // yyyyMMdd
    const randomNum = Math.random().toString().slice(2, 10); // 8位随机数
    
    // 流水号规则：商户号编码（5位）+日期（8位）+随机数（8位）
    return `${prefix}${date}${randomNum}`;
}
