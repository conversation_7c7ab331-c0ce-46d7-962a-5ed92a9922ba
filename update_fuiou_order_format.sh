#!/bin/bash

# 富有支付订单号格式修改脚本
# 使用方法：将此脚本上传到服务器 /www/wwwroot/writerpro.cn/ 目录下执行

echo "开始修改富有支付订单号格式..."

# 设置工作目录
cd /www/wwwroot/writerpro.cn

# 备份原文件
echo "备份原文件..."
cp backend/src/controllers/paymentController.js backend/src/controllers/paymentController.js.backup.$(date +%Y%m%d_%H%M%S)
cp frontend/script.js frontend/script.js.backup.$(date +%Y%m%d_%H%M%S)
cp backend/src/config/fuiouConfig.js backend/src/config/fuiouConfig.js.backup.$(date +%Y%m%d_%H%M%S)

# 修改后端 paymentController.js
echo "修改后端 paymentController.js..."
sed -i 's/const finalOrderId = orderId || `FY${Date.now()}${Math.random().toString(36).substr(2, 9)}`;/\/\/ 生成符合富有支付规则的订单号\n      const generateFuiouOrderId = () => {\n        const prefix = process.env.FUIOU_ORDER_PREFIX || '\''18927'\''; \/\/ 5位商户号编码\n        const date = new Date().toISOString().slice(0, 10).replace(\/-\/g, '\'\''); \/\/ yyyyMMdd\n        const randomNum = Math.random().toString().slice(2, 10); \/\/ 8位随机数\n        return `${prefix}${date}${randomNum}`;\n      };\n\n      const finalOrderId = orderId || generateFuiouOrderId();/g' backend/src/controllers/paymentController.js

# 修改前端 script.js
echo "修改前端 script.js..."
sed -i 's/function generateOrderId() {/function generateOrderId() {\n        const prefix = '\''18927'\''; \/\/ 5位商户号编码\n        const date = new Date().toISOString().slice(0, 10).replace(\/-\/g, '\'\''); \/\/ yyyyMMdd\n        const randomNum = Math.random().toString().slice(2, 10); \/\/ 8位随机数\n        return `${prefix}${date}${randomNum}`;/g' frontend/script.js

sed -i 's/return '\''FY'\'' + Date.now() + Math.random().toString(36).substr(2, 9);/\/\/ 已在上面修改为新格式/g' frontend/script.js

echo "修改完成！"
echo "重启服务..."
pm2 restart writerpro-backend

echo "检查服务状态..."
pm2 status

echo "所有修改已完成！新的订单号格式：商户号(5位) + 日期(8位) + 随机数(8位)"
